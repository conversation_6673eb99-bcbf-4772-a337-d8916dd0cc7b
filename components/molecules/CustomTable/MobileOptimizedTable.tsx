'use client';

import React, { useState } from 'react';
import {
  flexRender,
  getCoreRowModel,
  getPaginationRowModel,
  RowSelectionState,
  Updater,
  useReactTable,
  PaginationState,
  ColumnDef,
} from '@tanstack/react-table';
import { cn } from '@/utils/cn';
import { ChevronDown, ChevronRight, MoreHorizontal } from 'lucide-react';

interface MobileOptimizedTableProps<T> {
  columns: ColumnDef<T, any>[];
  tableData: T[];
  isLoading?: boolean;
  wrapperClass?: string;
  theadClass?: string;
  tbodyClass?: string;
  tableHeight?: string;
  handleClickTableRow?: (row: any) => void;
  manualPagination?: boolean;
  pageCount?: number;
  pagination?: PaginationState;
  onPaginationChange?: (updater: Updater<PaginationState>) => void;
  // Mobile-specific props
  mobileBreakpoint?: 'sm' | 'md' | 'lg';
  priorityColumns?: string[]; // Column keys to show on mobile
  mobileCardClassName?: string;
  showExpandToggle?: boolean;
}

export { MobileOptimizedTable };

export default function MobileOptimizedTable<T>({
  columns,
  tableData,
  isLoading = false,
  wrapperClass = '',
  theadClass = '',
  tbodyClass = '',
  tableHeight = 'h-full',
  handleClickTableRow,
  manualPagination = true,
  pageCount = -1,
  pagination,
  onPaginationChange,
  mobileBreakpoint = 'md',
  priorityColumns = [],
  mobileCardClassName = '',
  showExpandToggle = true,
}: MobileOptimizedTableProps<T>) {
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});
  const [_pagination, _setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });
  const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set());

  const effectivePagination = pagination !== undefined ? pagination : _pagination;
  const effectiveSetPagination = onPaginationChange !== undefined ? onPaginationChange : _setPagination;

  const handleRowSelection = (rowSelectFnc: Updater<RowSelectionState>) => {
    if (typeof rowSelectFnc !== 'function') return;
    const newSortState = rowSelectFnc(rowSelection);
    setRowSelection(newSortState);
  };

  const table = useReactTable({
    data: tableData,
    columns: columns,
    state: {
      rowSelection,
      pagination: effectivePagination,
    },
    enableColumnResizing: true,
    manualSorting: true,
    manualPagination: manualPagination,
    columnResizeMode: 'onChange',
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: manualPagination ? undefined : getPaginationRowModel(),
    enableRowSelection: true,
    onRowSelectionChange: handleRowSelection,
    onPaginationChange: manualPagination ? effectiveSetPagination : undefined,
    ...(manualPagination && pageCount !== -1 && { pageCount: pageCount }),
    getRowId: (row: any) => String(row?.id || Math.random()),
  });

  const toggleRowExpansion = (rowId: string) => {
    const newExpanded = new Set(expandedRows);
    if (newExpanded.has(rowId)) {
      newExpanded.delete(rowId);
    } else {
      newExpanded.add(rowId);
    }
    setExpandedRows(newExpanded);
  };

  // Get priority and secondary columns
  const mobileColumns = columns.filter(col => 
    priorityColumns.includes((col as any).accessorKey || col.id || '')
  );
  const secondaryColumns = columns.filter(col => 
    !priorityColumns.includes((col as any).accessorKey || col.id || '')
  );

  const tableRows = table?.getRowModel().rows;
  const isNodata = !isLoading && !tableRows?.length;

  const renderMobileCard = (row: any) => {
    const rowId = row.id;
    const isExpanded = expandedRows.has(rowId);
    const hasSecondaryColumns = secondaryColumns.length > 0;

    return (
      <div
        key={rowId}
        className={cn(
          'bg-white border border-gray-200 rounded-xl p-5 mb-4 shadow-sm hover:shadow-lg transition-all duration-200',
          mobileCardClassName,
          handleClickTableRow && 'cursor-pointer active:scale-[0.98]'
        )}
        onClick={() => handleClickTableRow?.(row)}
      >
        {/* Primary content */}
        <div className="space-y-4">
          {mobileColumns.map((column, index) => {
            const cell = row.getVisibleCells().find((c: any) => c.column.id === column.id);
            if (!cell) return null;

            return (
              <div key={cell.id} className={cn(
                "flex flex-col gap-2",
                index === 0 ? "pb-3 border-b border-gray-100" : ""
              )}>
                {index !== 0 && (
                  <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                    {typeof column.header === 'string' ? column.header : 'Field'}
                  </span>
                )}
                <div className="text-sm text-gray-900">
                  {flexRender(cell.column.columnDef.cell, cell.getContext())}
                </div>
              </div>
            );
          })}
        </div>

        {/* Expandable toggle */}
        {hasSecondaryColumns && showExpandToggle && (
          <button
            onClick={(e) => {
              e.stopPropagation();
              toggleRowExpansion(rowId);
            }}
            className="mt-4 w-full flex items-center justify-center text-blue-600 text-sm font-medium hover:text-blue-800 hover:bg-blue-50 py-2 rounded-lg transition-all duration-200"
          >
            {isExpanded ? (
              <>
                <ChevronDown className="w-4 h-4 mr-2" />
                Show Less Details
              </>
            ) : (
              <>
                <ChevronRight className="w-4 h-4 mr-2" />
                Show More Details
              </>
            )}
          </button>
        )}

        {/* Expandable content */}
        {isExpanded && hasSecondaryColumns && (
          <div className="mt-4 pt-4 border-t border-gray-100 space-y-4 bg-gray-50 -mx-5 -mb-5 px-5 pb-5 rounded-b-xl">
            <div className="text-xs font-medium text-gray-500 uppercase tracking-wide mb-3">
              Additional Details
            </div>
            {secondaryColumns.map((column) => {
              const cell = row.getVisibleCells().find((c: any) => c.column.id === column.id);
              if (!cell) return null;

              return (
                <div key={cell.id} className="flex flex-col gap-1">
                  <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                    {typeof column.header === 'string' ? column.header : 'Field'}
                  </span>
                  <div className="text-sm text-gray-900">
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>
    );
  };

  const renderDesktopTable = () => (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
      <div className={cn(
        'scrollbar max-h-[calc(100svh-210px)] overflow-y-auto',
        wrapperClass,
        {
          'overflow-y-hidden': isNodata || isLoading,
          [tableHeight]: !(isNodata || isLoading),
        }
      )}>
        <table className="table table-hover w-full">
          <thead className={cn('sticky top-0 z-10 overflow-y-hidden bg-gray-50', theadClass)}>
            {table.getHeaderGroups().map((headerGroup) => (
              <tr key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <th key={header.id} className="px-4 py-3 text-left text-sm font-medium text-gray-700 border-b border-gray-200">
                    {header.isPlaceholder
                      ? null
                      : flexRender(header.column.columnDef.header, header.getContext())}
                  </th>
                ))}
              </tr>
            ))}
          </thead>
          <tbody className={cn('z-1 sticky top-10 overflow-y-hidden bg-white', tbodyClass)}>
            {!isLoading &&
              tableRows?.map((row) => (
                <tr
                  key={row.id}
                  className="hover:bg-gray-50 transition-colors cursor-pointer border-b border-gray-100 last:border-b-0"
                  onClick={() => handleClickTableRow?.(row)}
                >
                  {row.getVisibleCells().map((cell) => (
                    <td key={cell.id} className="px-4 py-3 text-sm text-gray-900">
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </td>
                  ))}
                </tr>
              ))}
          </tbody>
        </table>
      </div>
    </div>
  );

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-gray-500">Loading...</div>
      </div>
    );
  }

  if (isNodata) {
    return (
      <div className="text-center py-8 text-gray-500">
        <p>No data available</p>
      </div>
    );
  }

  return (
    <div className="w-full">
      {/* Mobile view - Card layout */}
      <div className={cn(`block ${mobileBreakpoint}:hidden`)}>
        <div className="space-y-3">
          {tableRows?.map((row) => renderMobileCard(row))}
        </div>
      </div>

      {/* Desktop view - Table layout */}
      <div className={cn(`hidden ${mobileBreakpoint}:block`)}>
        {renderDesktopTable()}
      </div>
    </div>
  );
}
