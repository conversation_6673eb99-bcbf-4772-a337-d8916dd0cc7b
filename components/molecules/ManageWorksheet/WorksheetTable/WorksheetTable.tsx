'use client';
import React, { useState, useMemo, useCallback, useEffect } from 'react';
import {
  TWorksheet,
  WorksheetGeneratingStatus,
} from '@/apis/worksheet';
import { ERoutes } from '@/config/enums/enum';
import { getSocket } from '@/lib/socket';
import { useRouter } from 'next/navigation';
import dayjs from 'dayjs';
import { ColumnDef } from '@tanstack/react-table';
import {
  Loader2,
  Edit,
  Trash2,
  MoreHorizontal,
  AlertCircle,
  Search,
  X,
  FileText, // Icon for worksheets
  ChevronDown,
  ChevronUp,
  Download,
  Eye // Icon for View
} from 'lucide-react';

import { TablePagination, TablePaginationProps } from '../../TablePagination/TablePagination';
import Icon from '@/components/atoms/Icon'; // Keep for existing icons if needed
import { Button } from '@/components/atoms/Button/Button';
import { AnimationStyles } from '@/components/atoms/AnimationStyles/AnimationStyles'; // For loading/empty states
// Placeholder for future components - these will be created later
// import { WorksheetTableHeader } from '../../WorksheetTableHeader/WorksheetTableHeader';
// import { WorksheetTableFilterPanel } from '../../WorksheetTableFilterPanel/WorksheetTableFilterPanel';
// import { WorksheetTableBulkActions } from '../../WorksheetTableBulkActions/WorksheetTableBulkActions';
import { DeleteWorksheetModal } from '../../DeleteWorksheetModal/DeleteWorksheetModal';

// Temporary placeholder for StatusBadge and RoleBadge if needed, or create specific ones
import { StatusBadge } from '@/components/atoms/StatusBadge/StatusBadge'; // Assuming a generic status badge can be used

export interface WorksheetTableProps {
  worksheets: TWorksheet[];
  error: string | null;
  isLoading?: boolean;
  tableTitle?: string;
  entityName?: string;
  entityNamePlural?: string;
  createPath?: string; // e.g., for a "Create Worksheet" button
  // Pagination props from backend if server-side pagination is used
  currentPageBackend?: number;
  totalPagesBackend?: number;
  totalItemsBackend?: number;
  onBackendPageChange?: (page: number) => void;
  onBackendRowsPerPageChange?: (event: React.ChangeEvent<HTMLSelectElement>) => void;
  rowsPerPageBackend?: number;
}

// Helper to get nested properties safely
const getNestedValue = (obj: any, path: string, defaultValue: any = '') => {
  return path.split('.').reduce((acc, part) => acc && acc[part], obj) || defaultValue;
};


export const WorksheetTable: React.FC<WorksheetTableProps> = ({
  worksheets: initialWorksheets,
  error,
  isLoading = false,
  tableTitle = "Your Worksheets",
  entityName = "worksheet",
  entityNamePlural = "worksheets",
  createPath = ERoutes.MANAGE_WORKSHEET_CREATE, // Example, adjust as needed
  // Props for server-side pagination (if any)
  currentPageBackend,
  totalPagesBackend,
  totalItemsBackend,
  onBackendPageChange,
  onBackendRowsPerPageChange,
  rowsPerPageBackend,
}) => {
  const router = useRouter();
  const [worksheets, setWorksheets] = useState<TWorksheet[]>(initialWorksheets)
  // Modal state
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedWorksheet, setSelectedWorksheet] = useState<TWorksheet | null>(null);

  // Table state
  const [searchTerm, setSearchTerm] = useState('');
  const [sortColumn, setSortColumn] = useState<string | null>('createdAt'); // Default sort
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc'); // Default sort
  const [selectedWorksheetIds, setSelectedWorksheetIds] = useState<string[]>([]);
  const [isAllSelected, setIsAllSelected] = useState(false);
  const [currentPage, setCurrentPage] = useState(currentPageBackend || 1);
  const [rowsPerPage, setRowsPerPage] = useState(rowsPerPageBackend || 10);

  useEffect(() => {
    setCurrentPage(currentPageBackend !== undefined ? currentPageBackend : 1);
  }, [currentPageBackend]);

  useEffect(() => {
    setRowsPerPage(rowsPerPageBackend !== undefined ? rowsPerPageBackend : 10);
  }, [rowsPerPageBackend]);

  useEffect(() => {
    setWorksheets(initialWorksheets);
  }, [initialWorksheets]);

  // Socket listeners for real-time updates
  useEffect(() => {
    const socket = getSocket();
    const handleWorksheetUpdate = (updatedWorksheet: Partial<TWorksheet> & { id: string }) => {
      setWorksheets(prev =>
        prev.map(ws => (ws.id === updatedWorksheet.id ? { ...ws, ...updatedWorksheet } : ws))
      );
    };

    socket.on('worksheet:generated', ({ worksheetId }: { worksheetId: string }) => {
      console.log('worksheet:generated in table', worksheetId);
      handleWorksheetUpdate({ id: worksheetId, generatingStatus: WorksheetGeneratingStatus.GENERATED });
    });
    socket.on('worksheet:error', ({ worksheetId }: { worksheetId: string }) => {
      console.log('worksheet:error in table', worksheetId);
      handleWorksheetUpdate({ id: worksheetId, generatingStatus: WorksheetGeneratingStatus.ERROR });
    });
    // Progress might not change the table row status directly but could be used for other UI elements
    socket.on('worksheet:progress', (data: { worksheetId: string; progress: any }) => {
      // console.log('worksheet:progress in table', data.worksheetId);
    });

    return () => {
      socket.off('worksheet:generated');
      socket.off('worksheet:error');
      socket.off('worksheet:progress');
    };
  }, []);


  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
    if (!onBackendPageChange) setCurrentPage(1); else onBackendPageChange(1);
  };

  const getOptionValue = (options: TWorksheet['selectedOptions'] | undefined, key: string) => {
    return options?.find(opt => opt.optionType?.key === key)?.optionValue?.label || '';
  };

  const filteredAndSortedWorksheets = useMemo(() => {
    let result = [...worksheets];
    // Apply search
    if (searchTerm) {
      const lowerSearchTerm = searchTerm.toLowerCase();
      result = result.filter(ws =>
        ws.title?.toLowerCase().includes(lowerSearchTerm) ||
        ws.id.toLowerCase().includes(lowerSearchTerm) ||
        getOptionValue(ws.selectedOptions, 'grade').toLowerCase().includes(lowerSearchTerm) ||
        getOptionValue(ws.selectedOptions, 'topic').toLowerCase().includes(lowerSearchTerm)
      );
    }

    // Apply sorting
    if (sortColumn) {
      result.sort((a, b) => {
        let aValue: any;
        let bValue: any;

        if (sortColumn === 'title') {
          aValue = a.title || '';
          bValue = b.title || '';
        } else if (sortColumn === 'createdAt') {
          aValue = a.createdAt ? dayjs(a.createdAt).valueOf() : 0;
          bValue = b.createdAt ? dayjs(b.createdAt).valueOf() : 0;
        } else if (sortColumn === 'grade') {
          aValue = getOptionValue(a.selectedOptions, 'grade');
          bValue = getOptionValue(b.selectedOptions, 'grade');
        } else if (sortColumn === 'subject') {
          aValue = getOptionValue(a.selectedOptions, 'topic');
          bValue = getOptionValue(b.selectedOptions, 'topic');
        } else if (sortColumn === 'generatingStatus') { 
          // For sorting: 'Generated' (Active) > 'Pending' > 'Error' (Inactive)
          const statusOrder = {
            [WorksheetGeneratingStatus.GENERATED]: 0, // Active
            [WorksheetGeneratingStatus.PENDING]: 1,   // Inactive
            [WorksheetGeneratingStatus.ERROR]: 2,     // Inactive
          };
          aValue = statusOrder[a.generatingStatus] ?? 3; // Default to a lower priority if status is unknown
          bValue = statusOrder[b.generatingStatus] ?? 3;
        } else {
          aValue = getNestedValue(a, sortColumn, '');
          bValue = getNestedValue(b, sortColumn, '');
        }

        if (typeof aValue === 'string' && typeof bValue === 'string') {
          return sortDirection === 'asc' ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);
        }
        if (typeof aValue === 'number' && typeof bValue === 'number') { // This will handle generatingStatus sorting
          return sortDirection === 'asc' ? aValue - bValue : bValue - aValue;
        }
        // Boolean sorting removed as isActive is no longer used
        return 0;
      });
    }
    return result;
  }, [worksheets, searchTerm, sortColumn, sortDirection]);

  const handleOpenDeleteModal = useCallback((ws: TWorksheet) => {
    setSelectedWorksheet(ws);
    setIsDeleteModalOpen(true);
  }, []);

  const handleCloseDeleteModal = () => {
    setIsDeleteModalOpen(false);
    setSelectedWorksheet(null);
  };

  const handleDeleteSuccess = () => {
    if (selectedWorksheet) {
      setWorksheets(prev => prev.filter(ws => ws.id !== selectedWorksheet.id));
    }
    handleCloseDeleteModal(); // Ensure modal closes
  };

  const handleSort = useCallback((columnAccessor: string) => {
    if (sortColumn === columnAccessor) {
      setSortDirection(prev => (prev === 'asc' ? 'desc' : 'asc'));
    } else {
      setSortColumn(columnAccessor);
      setSortDirection('asc');
    }
  }, [sortColumn]);

  const handleSelectAll = useCallback(() => {
    if (isAllSelected) {
      setSelectedWorksheetIds([]);
    } else {
      setSelectedWorksheetIds(filteredAndSortedWorksheets.map(ws => ws.id));
    }
    setIsAllSelected(!isAllSelected);
  }, [isAllSelected, filteredAndSortedWorksheets]);

  const handleSelectWorksheet = useCallback((worksheetId: string) => {
    setSelectedWorksheetIds(prevSelected => {
      const newSelected = prevSelected.includes(worksheetId)
        ? prevSelected.filter(id => id !== worksheetId)
        : [...prevSelected, worksheetId];
      setIsAllSelected(newSelected.length === filteredAndSortedWorksheets.length && filteredAndSortedWorksheets.length > 0);
      return newSelected;
    });
  }, [filteredAndSortedWorksheets]);

  const handleBulkDelete = () => {
    alert(`Deleting ${selectedWorksheetIds.length} ${entityNamePlural}`);
    // Actual delete logic will go here (e.g., API call)
    setSelectedWorksheetIds([]);
    setIsAllSelected(false);
  };

  const handlePageChange = (page: number) => {
    if (onBackendPageChange) {
      onBackendPageChange(page);
    }
    setCurrentPage(page);
  };

  const handleRowsPerPageChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newRowsPerPage = Number(e.target.value);
    // Whether backend or client-side, update local state for TablePagination display
    setRowsPerPage(newRowsPerPage);
    setCurrentPage(1); // Always reset to first page on rows per page change

    if (onBackendRowsPerPageChange) {
      // If onBackendRowsPerPageChange is provided, WorksheetListing handles the actual data fetching logic.
      onBackendRowsPerPageChange(e);
    }
    // If not provided, local state change will trigger re-render for client-side pagination.
  };

  const columns = useMemo<ColumnDef<TWorksheet>[]>(() => [
    {
      id: 'select',
      header: () => (
        <input
          type="checkbox"
          checked={isAllSelected}
          onChange={handleSelectAll}
          className="h-4 w-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500"
          aria-label={`Select all ${entityNamePlural}`}
          disabled={filteredAndSortedWorksheets.length === 0}
        />
      ),
      cell: ({ row }) => (
        <input
          type="checkbox"
          checked={selectedWorksheetIds.includes(row.original.id)}
          onChange={() => handleSelectWorksheet(row.original.id)}
          onClick={(e) => e.stopPropagation()} // Prevent row click when clicking checkbox
          className="h-4 w-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500"
          aria-label={`Select ${row.original.title}`}
        />
      ),
      size: 40,
    },
    {
      accessorKey: 'title',
      header: () => (
        <button onClick={() => handleSort('title')} className="flex items-center gap-1">
          Name {sortColumn === 'title' && (sortDirection === 'asc' ? <ChevronUp size={14} /> : <ChevronDown size={14} />)}
        </button>
      ),
      cell: ({ row }) => {
        const ws = row.original;
        const grade = getOptionValue(ws.selectedOptions, 'grade');
        const subject = getOptionValue(ws.selectedOptions, 'topic'); // Assuming 'topic' is subject
        const language = getOptionValue(ws.selectedOptions, 'language');
        return (
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 rounded-md flex items-center justify-center bg-blue-100 text-blue-600">
              <FileText size={20} />
            </div>
            <div>
              <span className="font-medium text-gray-800 block text-sm">{ws.title}</span>
              <span className="text-xs text-gray-500 mt-0.5 inline-block">
                {grade && `${grade} `}
                {subject && `• ${subject} `}
                {language && `• ${language}`}
              </span>
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'createdAt',
      header: () => (
        <button onClick={() => handleSort('createdAt')} className="flex items-center gap-1">
          Created {sortColumn === 'createdAt' && (sortDirection === 'asc' ? <ChevronUp size={14} /> : <ChevronDown size={14} />)}
        </button>
      ),
      cell: ({ row }) => dayjs(row.original.createdAt).format('MMM DD, YYYY'),
    },
    {
      accessorKey: 'generatingStatus', 
      header: () => (
        <button onClick={() => handleSort('generatingStatus')} className="flex items-center gap-1">
          Status {sortColumn === 'generatingStatus' && (sortDirection === 'asc' ? <ChevronUp size={14} /> : <ChevronDown size={14} />)}
        </button>
      ),
      cell: ({ row }) => {
        const status = row.original.generatingStatus;
        let badgeType: 'active' | 'inactive' | 'pending' | 'error' = 'inactive'; // Default
        if (status === WorksheetGeneratingStatus.GENERATED) {
          badgeType = 'active';
        } else if (status === WorksheetGeneratingStatus.PENDING) {
          badgeType = 'pending';
        } else if (status === WorksheetGeneratingStatus.ERROR) {
          badgeType = 'error';
        }
        return <StatusBadge status={badgeType} />;
      },
    },
    {
      id: 'actions',
      header: () => <div className="text-right">Actions</div>,
      cell: ({ row }) => (
        <div className="flex items-center gap-1 justify-end">
          <Button
            onClick={(e) => {
              e.stopPropagation(); // Prevent row click when clicking delete button
              handleOpenDeleteModal(row.original);
            }}
            variant="ghost"
            className="p-2 w-auto h-auto rounded-lg text-red-600 hover:bg-red-50"
            title={`Delete ${entityName}`}
          >
            <Trash2 size={18} />
          </Button>
        </div>
      ),
    },
  ], [isAllSelected, handleSelectAll, selectedWorksheetIds, handleSelectWorksheet, handleOpenDeleteModal, filteredAndSortedWorksheets.length, sortColumn, sortDirection, entityName, entityNamePlural, router, handleSort]);

  // Handle row click to navigate to detail page
  const handleRowClick = (ws: TWorksheet) => {
    router.push(`${ERoutes.MANAGE_WORKSHEET_REVIEW}&id=${ws.id}`);
  };

  const currentTotalItems = totalItemsBackend ?? filteredAndSortedWorksheets.length;
  const currentTotalPages = totalPagesBackend ?? Math.ceil(filteredAndSortedWorksheets.length / rowsPerPage);
  const paginatedWorksheets = onBackendPageChange ? worksheets : filteredAndSortedWorksheets.slice((currentPage - 1) * rowsPerPage, currentPage * rowsPerPage);
  console.log('totalItemsBackend',totalItemsBackend)

  // Placeholder for WorksheetTableHeader
  const WorksheetTableHeaderComponent = () => (
    <div className="p-4 border-b border-gray-200 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
      <div>
        <h2 className="text-xl font-semibold text-gray-800">{tableTitle}</h2>
        <p className="text-sm text-gray-500 mt-1">
          {isLoading ? `Loading ${entityNamePlural}...` : `${currentTotalItems} ${currentTotalItems === 1 ? entityName : entityNamePlural} found`}
        </p>
      </div>
      <div className="flex items-center gap-2 w-full sm:w-auto">
        <div className="relative flex-grow sm:flex-grow-0 sm:w-64">
          <Search size={18} className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400" />
          <input
            type="text"
            placeholder={`Search ${entityNamePlural}...`}
            value={searchTerm}
            onChange={handleSearchChange}
            className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
          />
        </div>
        {/* <button className="p-2 border border-gray-300 rounded-lg hover:bg-gray-100 text-gray-500 transition-colors" title="Export Data">
          <Download size={20} />
        </button> */}
      </div>
    </div>
  );

  // Placeholder for WorksheetTableBulkActions
  const WorksheetTableBulkActionsComponent = () => (
    <div className="p-3 border-b border-gray-200 bg-blue-50 flex justify-between items-center">
      <p className="text-sm text-blue-700">
        {selectedWorksheetIds.length} {selectedWorksheetIds.length === 1 ? entityName : entityNamePlural} selected
      </p>
      <div className="flex gap-2">
        <button
          onClick={() => { setSelectedWorksheetIds([]); setIsAllSelected(false); }}
          className="px-3 py-1.5 text-sm text-blue-600 hover:bg-blue-100 rounded-md"
        >
          Clear Selection
        </button>
        <button
          onClick={handleBulkDelete}
          className="px-3 py-1.5 text-sm bg-red-500 text-white hover:bg-red-600 rounded-md flex items-center gap-1.5"
        >
          <Trash2 size={16} /> Delete Selected
        </button>
      </div>
    </div>
  );

  return (
    <div className="overflow-hidden rounded-lg border border-gray-200 shadow-sm bg-white">
      <style dangerouslySetInnerHTML={{ __html: AnimationStyles }} />
      {/* <WorksheetTableHeader /> */}
      <WorksheetTableHeaderComponent />

      {selectedWorksheetIds.length > 0 && (
        /* <WorksheetTableBulkActions /> */
        <WorksheetTableBulkActionsComponent />
      )}

      <div className="overflow-x-auto scrollbar max-h-[calc(100svh-280px)]"> {/* Adjusted height */}
        {/* Added min-width */}
        <table className="w-full min-w-[800px]">
          <thead className="sticky top-0 z-10 bg-gray-100 border-b border-gray-200">
            <tr>
              {columns.map(column => (
                <th
                  key={column.id || (column as any).accessorKey}
                  className="py-3 px-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider"
                  style={{ width: column.size ? `${column.size}px` : '' }}
                >
                  {typeof column.header === 'function' ? column.header({} as any) : column.header}
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200">
            {isLoading ? (
              <tr>
                <td colSpan={columns.length} className="px-6 py-20 text-center">
                  <div className="flex flex-col items-center justify-center">
                    <Loader2 size={40} className="animate-spin text-blue-600 mb-4" />
                    <p className="text-lg font-medium text-gray-700">Loading {entityNamePlural}...</p>
                    <p className="text-sm text-gray-500 mt-1">Please wait a moment.</p>
                  </div>
                </td>
              </tr>
            ) : paginatedWorksheets.length > 0 ? (
              paginatedWorksheets.map((ws) => (
                <tr
                  key={ws.id}
                  className="hover:bg-gray-50 transition-colors duration-150 cursor-pointer"
                  onClick={() => handleRowClick(ws)}
                >
                  {columns.map(column => (
                    <td key={column.id || (column as any).accessorKey} className="py-3 px-4 text-sm text-gray-700 whitespace-nowrap">
                      {typeof column.cell === 'function' ? column.cell({ row: { original: ws } } as any) : (ws as any)[(column as any).accessorKey]}
                    </td>
                  ))}
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={columns.length} className="px-6 py-20 text-center">
                  {error ? (
                    <div className="flex flex-col items-center justify-center">
                      <AlertCircle size={48} className="text-red-500 mb-4" />
                      <p className="text-xl font-medium text-gray-700 mb-2">Error loading {entityNamePlural}</p>
                      <p className="text-sm text-gray-500 max-w-md">{error}</p>
                    </div>
                  ) : (
                    <div className="flex flex-col items-center justify-center">
                      <FileText size={48} className="text-gray-400 mb-4" />
                      <p className="text-xl font-medium text-gray-700 mb-2">No {entityNamePlural} found</p>
                      <p className="text-sm text-gray-500 mb-6">Create a new {entityName} to get started.</p>
                      {createPath && (
                        <Button
                          href={createPath}
                          variant="primary"
                          className="shadow-sm hover:shadow-md text-white transition-shadow px-5 py-2.5 flex items-center gap-2 w-auto"
                          iconProps={{ variant: 'plus', className:'w-5' }}
                        >
                          Create {entityName.charAt(0).toUpperCase() + entityName.slice(1)}
                        </Button>
                      )}
                    </div>
                  )}
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {currentTotalItems > 0 && !isLoading && (
        <TablePagination
          currentPage={currentPage}
          totalPages={currentTotalPages}
          rowsPerPage={rowsPerPage}
          totalItems={currentTotalItems}
          onPageChange={handlePageChange}
          onRowsPerPageChange={handleRowsPerPageChange}
        />
      )}

      {selectedWorksheet && isDeleteModalOpen && (
        <DeleteWorksheetModal
          isOpen={isDeleteModalOpen}
          onClose={handleCloseDeleteModal}
          worksheet={selectedWorksheet}
          onSuccess={handleDeleteSuccess}
        />
      )}
    </div>
  );
};
