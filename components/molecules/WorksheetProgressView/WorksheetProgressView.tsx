'use client';

import { Question } from '@/components/molecules/QuestionListingView/QuestionListingView';
import { LoadingWorksheetScreen } from '@/components/molecules/LoadingWorksheetScreen/LoadingWorksheetScreen';
import QuestionListingView from '@/components/molecules/QuestionListingView/QuestionListingView';
import { Button } from '@/components/atoms/Button/Button';
import Icon from '@/components/atoms/Icon';
import Link from 'next/link';
import { WorksheetGeneratingStatus } from '@/apis/worksheet';
import { ProgressData } from '@/components/molecules/ProgressBar/ProgressBar';
import { useWorksheetProgress } from '@/hooks/useWorksheetProgress';
import { useState } from 'react';
import { PrintModal } from '@/components/molecules/PrintModal';
import { Breadcrumb } from '@/components/atoms/Breadcrumb/Breadcrumb';
import { useSession } from 'next-auth/react';
import { EUserRole } from '@/config/enums/user';
import { HomeIcon, ClipboardListIcon } from 'lucide-react';


type WorksheetProgressViewProps = {
  worksheetId: string;
  initialStatus: WorksheetGeneratingStatus;
  initialQuestions?: Question[];
  initialProgress?: ProgressData;
  worksheetInfo?: {
    topic?: string;
    subject?: string; // Added subject
    grade?: string;
    language?: string;
    level?: string;
    totalQuestions?: number;
  };
  schoolInfo?: {
    name: string;
    address?: string;
    phoneNumber?: string;
    registeredNumber?: string;
    email?: string;
    logoUrl?: string;
  };
};

export const WorksheetProgressView: React.FC<WorksheetProgressViewProps> = ({
  worksheetId,
  initialStatus,
  initialQuestions = [],
  initialProgress,
  worksheetInfo,
  schoolInfo,
}) => {
  const { status, questions } = useWorksheetProgress({
    worksheetId,
    initialStatus,
    initialQuestions,
    initialProgress,
  });

  // State for print modal
  const [isPrintModalOpen, setIsPrintModalOpen] = useState(false);


  // If status is pending, show the loading screen with progress
  if (status === WorksheetGeneratingStatus.PENDING) {
    return (
      <LoadingWorksheetScreen
        worksheetId={worksheetId}
        initialQuestions={initialQuestions}
        initialProgress={initialProgress}
      />
    );
  }

  // If status is generated or error, show the completed worksheet
  return (
    <div className="w-full h-full flex flex-col">
      <div className="flex-1 overflow-auto">
        <div className="w-full max-w-5xl mx-auto">
          <div className="w-full">
            {status === WorksheetGeneratingStatus.ERROR ? (
              <div className="w-full p-8 text-center">
                <div className="text-error text-xl mb-4">
                  An error occurred while generating the worksheet
                </div>
                <div className="text-gray-500">
                  Please try again or contact support if the problem persists.
                </div>
              </div>
            ) : (
              <>
                <QuestionListingView
                  questions={questions}
                  containerClass="pb-20"
                  isHtmlContent
                  worksheetInfo={worksheetInfo}
                />
                <div
                  className="fixed item-center gap-6 bg-white flex justify-between items-center w-[calc(100%-280px)] bottom-0 h-[60px] px-6 shadow-sm"
                  style={{ left: '310px', width: 'calc(100vw - 310px)' }}
                >
                  <div className="w-9 h-9 rounded-full bg-gray-100 flex items-center justify-center cursor-pointer hover:bg-gray-200 transition-colors">
                    <Link href="/manage-worksheet">
                      <Icon
                        variant="chevron-down"
                        className="rotate-90"
                        size={3.5}
                      />
                    </Link>
                  </div>
                  <div className="flex gap-6 items-center">
                    <Button  onClick={() => setIsPrintModalOpen(true)} className="p-3 h-10 w-fit">Print</Button>
                  </div>

                  {/* Print Modal */}
                  <PrintModal
                    isOpen={isPrintModalOpen}
                    onClose={() => setIsPrintModalOpen(false)}
                    questions={questions}
                    worksheetInfo={worksheetInfo}
                    schoolInfo={schoolInfo}
                  />

                </div>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
